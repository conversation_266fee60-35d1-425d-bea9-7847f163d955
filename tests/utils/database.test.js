const {
    transformJsonAttributes,
    ReturnValues
  } = require('../../src/utils/database');
  
  describe('transformJsonAttributes function', () => {
    it('should generate a correct update expression for given attributes', () => {
      const attributes = { name: '<PERSON>', age: 30 };
      const result = transformJsonAttributes(attributes);
  
      expect(result.UpdateExpression).toBe('SET #name = :name, #age = :age');
      expect(result.ExpressionAttributeNames).toEqual({
        '#name': 'name',
        '#age': 'age'
      });
      expect(result.ExpressionAttributeValues).toEqual({
        ':name': '<PERSON>',
        ':age': 30
      });
      expect(result.ReturnValues).toBe(ReturnValues.UPDATED_NEW);
    });
  
    it('should return correct ReturnValues when specified', () => {
      const attributes = { city: 'Pasig' };
  
      expect(
        transformJsonAttributes(attributes, ReturnValues.ALL_NEW).ReturnValues
      ).toBe(ReturnValues.ALL_NEW);
      expect(
        transformJsonAttributes(attributes, ReturnValues.ALL_OLD).ReturnValues
      ).toBe(ReturnValues.ALL_OLD);
      expect(
        transformJsonAttributes(attributes, ReturnValues.UPDATED_OLD).ReturnValues
      ).toBe(ReturnValues.UPDATED_OLD);
    });
  
    it('should default to UPDATED_NEW if an invalid returnValues is provided', () => {
      const attributes = { country: 'Manila' };
      const result = transformJsonAttributes(attributes, 'INVALID_VALUE');
      expect(result.ReturnValues).toBe(ReturnValues.UPDATED_NEW);
    });
  });
  