const {
    paymentProcessedPayload,
    getStatus
  } = require('../../src/utils/channel'); // Replace with actual module path
  
  describe('getStatus', () => {
    test('should return correct mapped status', () => {
      expect(getStatus('ACTIVE')).toBe('ECPAY_GENERATED');
      expect(getStatus('COMPLETED')).toBe('ECPAY_AUTHORISED');
      expect(getStatus('INACTIVE')).toBe('ECPAY_DROPPED');
      expect(getStatus('EXPIRED')).toBe('ECPAY_EXPIRED');
    });
  
    test('should throw an error for invalid status', () => {
      expect(() => getStatus('INVALID_STATUS')).toThrow(
        'Invalid status: INVALID_STATUS'
      );
    });
  });
  
  describe('paymentProcessedPayload', () => {
    test('should generate correct payload', () => {
      const input = {
        paymentId: '98765',
        status: 'ECPAY_AUTHORISED',
        paymentCode: 'XYZ789',
        expiry: '2023-12-31T23:59:59Z'
      };
  
      const expectedOutput = {
        notification: {
          name: 'PaymentProcessed',
          payload: {
            paymentId: '98765',
            accounts: [
              {
                status: 'ECPAY_AUTHORISED',
                paymentCode: 'XYZ789',
                expiry: '2023-12-31T23:59:59Z'
              }
            ]
          }
        }
      };
  
      expect(paymentProcessedPayload(input)).toEqual(expectedOutput);
    });
  });
  