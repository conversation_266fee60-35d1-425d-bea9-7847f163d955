const {
    getPhDateTimeString,
    getPreviousOneHourPhTime,
    getPhDateTime,
    getTodayTimestamp
  } = require('@utils/date');
  
  describe('Date Time Utility Functions', () => {
    beforeAll(() => {
      jest.useFakeTimers();
    });
  
    afterAll(() => {
      jest.useRealTimers();
    });
  
    it('should return a valid Date object for getPhDateTime', () => {
      const mockDate = new Date('2024-02-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);
  
      const result = getPhDateTime();
      expect(result).toBeInstanceOf(Date);
      expect(result.toISOString()).toBe(
        new Date(mockDate.getTime() + 28_800_000).toISOString()
      );
    });
  
    it('should return the correct timestamp for getTodayTimestamp', () => {
      const mockDate = new Date('2024-02-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);
  
      const expectedTimestamp = new Date(2024, 1, 20, 11, 59, 59).getTime(); // Month is zero-indexed
      expect(getTodayTimestamp()).toBe(expectedTimestamp);
    });
  
    it('should return a valid ISO string for getPhDateTimeString', () => {
      const mockDate = new Date('2024-02-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);
  
      const expectedISO = new Date(mockDate.getTime() + 28_800_000).toISOString();
      expect(getPhDateTimeString()).toBe(expectedISO);
    });
  
    it('should return the correct ISO string for getPreviousOneHourPhTime', () => {
      const mockDate = new Date('2024-02-20T10:00:00.000Z');
      jest.setSystemTime(mockDate);
  
      const expectedISO = new Date(
        mockDate.getTime() + 28_800_000 - 3_600_000
      ).toISOString();
      expect(getPreviousOneHourPhTime()).toBe(expectedISO);
    });
  });
  