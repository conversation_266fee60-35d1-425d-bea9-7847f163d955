const SampleService = require('../../src/services/SampleService');
const logger = require('@configs/logger');
const { transactionLogsRepository } = require('@repositories');

// Mock dependencies
jest.mock('@configs/logger', () => ({
  info: jest.fn()
}));

jest.mock('@repositories', () => ({
  transactionLogsRepository: {
    getAll: jest.fn()
  }
}));

describe('SampleService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getAll', () => {
    it('should log info message when getting all transaction logs', async () => {
      // Mock the repository response
      const mockResponse = {
        items: [{ id: '1' }, { id: '2' }],
        count: 2,
        lastEvaluatedKey: null
      };
      transactionLogsRepository.getAll.mockResolvedValue(mockResponse);

      // Call the service method
      const result = await SampleService.getAll();

      // Verify logger was called with the correct message
      expect(logger.info).toHaveBeenCalledTimes(1);
      expect(logger.info).toHaveBeenCalledWith('Getting all transaction logs');

      // Verify repository method was called
      expect(transactionLogsRepository.getAll).toHaveBeenCalledTimes(1);

      // Verify the result is correct
      expect(result).toEqual(mockResponse);
    });

    it('should propagate errors from the repository', async () => {
      // Mock the repository to throw an error
      const mockError = new Error('Database connection error');
      transactionLogsRepository.getAll.mockRejectedValue(mockError);

      // Call the service method and expect it to throw
      await expect(SampleService.getAll()).rejects.toThrow(
        'Database connection error'
      );

      // Verify logger was still called
      expect(logger.info).toHaveBeenCalledTimes(1);
      expect(logger.info).toHaveBeenCalledWith('Getting all transaction logs');
    });

    it('should handle empty results from the repository', async () => {
      // Mock empty response
      const mockEmptyResponse = {
        items: [],
        count: 0,
        lastEvaluatedKey: null
      };
      transactionLogsRepository.getAll.mockResolvedValue(mockEmptyResponse);

      // Call the service method
      const result = await SampleService.getAll();

      // Verify the result is correct
      expect(result).toEqual(mockEmptyResponse);
    });
  });
});
