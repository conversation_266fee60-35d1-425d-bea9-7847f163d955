const DDBTransactionService = require('../../src/services/DDBTransactionService');
const ddbClient = require('@configs/ddb');
const { TransactWriteCommand } = require('@aws-sdk/lib-dynamodb');

// Mock the DDB client and utils
jest.mock('@configs/ddb', () => ({
  send: jest.fn()
}));

jest.mock('@utils/database', () => ({
  transactionOperationWrapper: jest.fn((params, operation) => {
    if (operation === 'Put') {
      return { Put: params };
    } else if (operation === 'Update') {
      return { Update: params };
    } else if (operation === 'Delete') {
      return { Delete: params };
    }
    return { [operation]: params };
  })
}));

describe('DDBTransactionService', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should be a singleton', () => {
    const instance1 = require('../../src/services/DDBTransactionService');
    const instance2 = require('../../src/services/DDBTransactionService');

    expect(instance1).toBe(instance2);
  });

  it('should throw an error when operations array is empty', async () => {
    await expect(DDBTransactionService.writeDDBTransaction([])).rejects.toThrow(
      'Invalid operations array'
    );
  });

  it('should throw an error when operations is not an array', async () => {
    await expect(DDBTransactionService.writeDDBTransaction({})).rejects.toThrow(
      'Invalid operations array'
    );
  });

  it('should execute a transaction with Put operations', async () => {
    const mockOperations = [
      {
        params: {
          TableName: 'TestTable',
          Item: { id: '123', name: 'Test Item' }
        },
        operation: 'Put'
      },
      {
        params: {
          TableName: 'TestTable',
          Item: { id: '456', name: 'Another Test Item' }
        },
        operation: 'Put'
      }
    ];

    ddbClient.send.mockResolvedValue({ success: true });

    const result =
      await DDBTransactionService.writeDDBTransaction(mockOperations);

    expect(ddbClient.send).toHaveBeenCalledTimes(1);
    expect(ddbClient.send).toHaveBeenCalledWith(
      expect.objectContaining({
        input: {
          TransactItems: [
            { Put: mockOperations[0].params },
            { Put: mockOperations[1].params }
          ]
        }
      })
    );
    // Verify that TransactWriteCommand was used
    expect(ddbClient.send.mock.calls[0][0]).toBeInstanceOf(
      TransactWriteCommand
    );
    expect(result).toEqual({ success: true });
  });

  it('should execute a transaction with mixed operations', async () => {
    const mockOperations = [
      {
        params: {
          TableName: 'TestTable',
          Item: { id: '123', name: 'Test Item' }
        },
        operation: 'Put'
      },
      {
        params: {
          TableName: 'TestTable',
          Key: { id: '456' }
        },
        operation: 'Delete'
      },
      {
        params: {
          TableName: 'TestTable',
          Key: { id: '789' },
          UpdateExpression: 'SET #name = :name',
          ExpressionAttributeNames: { '#name': 'name' },
          ExpressionAttributeValues: { ':name': 'Updated Name' }
        },
        operation: 'Update'
      }
    ];

    ddbClient.send.mockResolvedValue({ success: true });

    const result =
      await DDBTransactionService.writeDDBTransaction(mockOperations);

    expect(ddbClient.send).toHaveBeenCalledTimes(1);
    expect(ddbClient.send).toHaveBeenCalledWith(
      expect.objectContaining({
        input: {
          TransactItems: [
            { Put: mockOperations[0].params },
            { Delete: mockOperations[1].params },
            { Update: mockOperations[2].params }
          ]
        }
      })
    );
    // Verify that TransactWriteCommand was used
    expect(ddbClient.send.mock.calls[0][0]).toBeInstanceOf(
      TransactWriteCommand
    );
    expect(result).toEqual({ success: true });
  });

  it('should default to Put operation if not specified', async () => {
    const mockOperations = [
      {
        params: {
          TableName: 'TestTable',
          Item: { id: '123', name: 'Test Item' }
        }
        // No operation specified, should default to Put
      }
    ];

    ddbClient.send.mockResolvedValue({ success: true });

    await DDBTransactionService.writeDDBTransaction(mockOperations);

    expect(ddbClient.send).toHaveBeenCalledWith(
      expect.objectContaining({
        input: {
          TransactItems: [{ Put: mockOperations[0].params }]
        }
      })
    );
    // Verify that TransactWriteCommand was used
    expect(ddbClient.send.mock.calls[0][0]).toBeInstanceOf(
      TransactWriteCommand
    );
  });

  it('should propagate errors from DDB client', async () => {
    const mockOperations = [
      {
        params: {
          TableName: 'TestTable',
          Item: { id: '123', name: 'Test Item' }
        },
        operation: 'Put'
      }
    ];

    const mockError = new Error('DDB connection error');
    ddbClient.send.mockRejectedValueOnce(mockError);

    await expect(
      DDBTransactionService.writeDDBTransaction(mockOperations)
    ).rejects.toThrow('DDB connection error');

    expect(ddbClient.send).toHaveBeenCalledTimes(1);
    expect(ddbClient.send.mock.calls[0][0]).toBeInstanceOf(
      TransactWriteCommand
    );
  });
});
