const mockPino = jest.fn();
// Mock dependencies
jest.mock('pino', () => mockPino);
jest.mock('@configs/env', () => ({
  LOG_LEVEL: 'info',
  NODE_ENV: 'local'
}));

describe('Logger Configuration', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    mockPino.mockReturnValue({ info: jest.fn(), error: jest.fn() });

    // Clear the module cache before each test to ensure fresh imports
    jest.resetModules();
  });

  it('should initialize logger with correct configuration', () => {
    // Import the logger module (which will use our mocks)
    require('@configs/logger');

    // Verify pino was called with the expected configuration
    expect(mockPino).toHaveBeenCalledTimes(1);
    expect(mockPino).toHaveBeenCalledWith({
      level: 'info',
      serializers: {
        pid: expect.any(Function),
        hostname: expect.any(Function)
      },
      transport: { target: 'pino-pretty' }
    });
  });

  it('should have serializers that return undefined', () => {
    // Import the logger module
    require('@configs/logger');

    // Extract the serializers from the call to pino
    const config = mockPino.mock.calls[0][0];
    const { serializers } = config;

    // Test that serializers return undefined
    expect(serializers.pid()).toBeUndefined();
    expect(serializers.hostname()).toBeUndefined();
  });

  it('should use different transport config based on NODE_ENV', () => {
    // Mock a different environment
    jest.mock(
      '@configs/env',
      () => ({
        LOG_LEVEL: 'debug',
        NODE_ENV: 'production'
      }),
      { virtual: true }
    );

    // Import the logger module again with new mocks
    require('@configs/logger');

    // For production, it should not include the transport config
    const config = mockPino.mock.calls[0][0];
    expect(config.transport).toStrictEqual({ target: 'pino-pretty' });
  });
});
