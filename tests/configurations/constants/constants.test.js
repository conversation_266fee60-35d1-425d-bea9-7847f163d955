'use strict';

const Constants = require('@configs/constants/constants');

describe('Constants', () => {
  it('should define ECPAY_GENERATED_STATUS constant', () => {
    expect(Constants.ECPAY_GENERATED_STATUS).toBe('ECPAY_GENERATED');
  });

  it('should be a frozen object', () => {
    expect(Object.isFrozen(Constants)).toBe(true);
  });

  it('should throw error when trying to modify constants', () => {
    expect(() => {
      Constants.ECPAY_GENERATED_STATUS = 'NEW_VALUE';
    }).toThrow(TypeError);
  });

  it('should not allow adding new properties', () => {
    expect(() => {
      Constants.NEW_PROPERTY = 'NEW_VALUE';
    }).toThrow(TypeError);
  });
});
