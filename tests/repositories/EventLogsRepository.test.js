const EventLogsRepository = require('@repositories/EventLogsRepository');
const BareRepository = require('@root/src/repositories/BaseRepository');
const { DDB_TABLE_EVENT_LOGS } = require('@configs/env');

describe('EventLogsRepository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Singleton Pattern', () => {
    test('should be a singleton instance', () => {
      const instance1 = EventLogsRepository;
      const instance2 = require('@repositories/EventLogsRepository');
      expect(instance1).toBe(instance2);
    });

    test('getInstance should return the existing instance if already created', () => {
      // Store the original instance
      const originalInstance = EventLogsRepository;

      // Access the class constructor through the instance's constructor property
      const EventLogsRepositoryClass = originalInstance.constructor;

      // Call getInstance directly
      const newInstance = EventLogsRepositoryClass.getInstance();

      // Should return the same instance
      expect(newInstance).toBe(originalInstance);
    });
  });

  describe('Inheritance', () => {
    test('should extend BareRepository', () => {
      expect(EventLogsRepository).toBeInstanceOf(BareRepository);
    });

    test('should initialize with the correct table name', () => {
      expect(EventLogsRepository.tableName).toBe(DDB_TABLE_EVENT_LOGS);
    });
  });

  describe('Basic Repository Operations', () => {
    test('should have access to inherited methods from BareRepository', () => {
      // Verify that common repository methods are available
      expect(typeof EventLogsRepository.get).toBe('function');
      expect(typeof EventLogsRepository.save).toBe('function');
      expect(typeof EventLogsRepository.update).toBe('function');
      expect(typeof EventLogsRepository.delete).toBe('function');
      expect(typeof EventLogsRepository.query).toBe('function');
    });
  });
});
