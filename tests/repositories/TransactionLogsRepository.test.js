const TransactionLogsRepository = require('@repositories/TransactionLogsRepository');
const BareRepository = require('@root/src/repositories/BaseRepository');
const {
  DDB_TABLE_TRANSACTION_LOGS,
  DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX
} = require('@configs/env');
const ddbClient = require('@configs/ddb');

// Mock the DynamoDB client
jest.mock('@configs/ddb', () => ({
  send: jest.fn()
}));

describe('TransactionLogsRepository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Singleton Pattern', () => {
    test('should be a singleton instance', () => {
      const instance1 = TransactionLogsRepository;
      const instance2 = require('@repositories/TransactionLogsRepository');
      expect(instance1).toBe(instance2);
    });

    test('createInstance should return the existing instance if already created', () => {
      // Store the original instance
      const originalInstance = TransactionLogsRepository;

      // Access the class constructor through the instance's constructor property
      const TransactionLogsRepositoryClass = originalInstance.constructor;

      // Call createInstance directly
      const newInstance = TransactionLogsRepositoryClass.createInstance();

      // Should return the same instance
      expect(newInstance).toBe(originalInstance);
    });
  });

  describe('Inheritance', () => {
    test('should extend BareRepository', () => {
      expect(TransactionLogsRepository).toBeInstanceOf(BareRepository);
    });

    test('should initialize with the correct table name', () => {
      expect(TransactionLogsRepository.tableName).toBe(
        DDB_TABLE_TRANSACTION_LOGS
      );
    });
  });
  describe('Basic Repository Operations', () => {
    test('should have access to inherited methods from BareRepository', () => {
      // Verify that common repository methods are available
      expect(typeof TransactionLogsRepository.get).toBe('function');
      expect(typeof TransactionLogsRepository.save).toBe('function');
      expect(typeof TransactionLogsRepository.update).toBe('function');
      expect(typeof TransactionLogsRepository.delete).toBe('function');
      expect(typeof TransactionLogsRepository.query).toBe('function');
    });
  });

  describe('getExpiredTransactions', () => {
    test('should query expired transactions with correct parameters', async () => {
      const currentDateTime = '2024-03-01T00:00:00Z';
      const status = 'PENDING';
      const limit = 500;
      const mockItems = [{ id: '1' }, { id: '2' }];

      ddbClient.send.mockResolvedValueOnce({
        Items: mockItems,
        LastEvaluatedKey: { id: '2' }
      });

      const result = await TransactionLogsRepository.getExpiredTransactions(
        { currentDateTime, status },
        limit
      );

      // Check that the command input has the expected properties
      expect(ddbClient.send.mock.calls[0][0].input).toEqual(
        expect.objectContaining({
          TableName: DDB_TABLE_TRANSACTION_LOGS,
          IndexName: DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX,
          KeyConditionExpression: '#status = :status',
          FilterExpression: 'transactionExpiry <= :now',
          ExpressionAttributeNames: {
            '#status': 'status'
          },
          ExpressionAttributeValues: {
            ':now': currentDateTime,
            ':status': status
          },
          Limit: limit
        })
      );

      expect(result).toEqual({
        items: mockItems,
        lastEvaluatedKey: { id: '2' }
      });
    });

    test('should use default limit of 1000 if not provided', async () => {
      const currentDateTime = '2024-03-01T00:00:00Z';
      const status = 'PENDING';
      const defaultLimit = 1000;

      ddbClient.send.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: null
      });

      await TransactionLogsRepository.getExpiredTransactions({
        currentDateTime,
        status
      });

      // Check that the command input has the expected limit
      expect(ddbClient.send.mock.calls[0][0].input).toEqual(
        expect.objectContaining({
          Limit: defaultLimit
        })
      );
    });

    test('should include lastEvaluatedKey in query if provided', async () => {
      const currentDateTime = '2024-03-01T00:00:00Z';
      const status = 'PENDING';
      const lastEvaluatedKey = { id: '5' };

      ddbClient.send.mockResolvedValueOnce({
        Items: [],
        LastEvaluatedKey: null
      });

      await TransactionLogsRepository.getExpiredTransactions(
        { currentDateTime, status },
        1000,
        lastEvaluatedKey
      );

      // Check that the command input has the expected ExclusiveStartKey
      expect(ddbClient.send.mock.calls[0][0].input).toEqual(
        expect.objectContaining({
          ExclusiveStartKey: lastEvaluatedKey
        })
      );
    });

    test('should handle empty results', async () => {
      ddbClient.send.mockResolvedValueOnce({});

      const result = await TransactionLogsRepository.getExpiredTransactions({
        currentDateTime: '2024-03-01T00:00:00Z',
        status: 'PENDING'
      });

      expect(result).toEqual({
        items: [],
        lastEvaluatedKey: undefined
      });
    });

    test('should handle null Items in response', async () => {
      ddbClient.send.mockResolvedValueOnce({
        Items: null
      });

      const result = await TransactionLogsRepository.getExpiredTransactions({
        currentDateTime: '2024-03-01T00:00:00Z',
        status: 'PENDING'
      });

      expect(result).toEqual({
        items: [],
        lastEvaluatedKey: undefined
      });
    });

    test('should handle different status values', async () => {
      const statuses = ['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'];

      for (const status of statuses) {
        ddbClient.send.mockResolvedValueOnce({
          Items: [{ id: '1', status }]
        });

        await TransactionLogsRepository.getExpiredTransactions({
          currentDateTime: '2024-03-01T00:00:00Z',
          status
        });

        // Check that the command input has the expected status value
        expect(
          ddbClient.send.mock.calls[statuses.indexOf(status)][0].input
            .ExpressionAttributeValues
        ).toEqual(
          expect.objectContaining({
            ':status': status
          })
        );
      }
    });

    test('should propagate errors from DDB client', async () => {
      const error = new Error('DDB connection error');
      ddbClient.send.mockRejectedValueOnce(error);

      await expect(
        TransactionLogsRepository.getExpiredTransactions({
          currentDateTime: '2024-03-01T00:00:00Z',
          status: 'PENDING'
        })
      ).rejects.toThrow('DDB connection error');
    });
  });
});
