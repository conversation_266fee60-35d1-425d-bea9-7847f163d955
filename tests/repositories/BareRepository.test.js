const ddbClient = require('@configs/ddb');

const BareRepository = require('@root/src/repositories/BaseRepository');
const {
  DeleteCommand,
  GetCommand,
  PutCommand,
  QueryCommand,
  ScanCommand,
  UpdateCommand
} = require('@aws-sdk/lib-dynamodb');
const { ReturnValues } = require('@utils/database');

describe('#BareRepository', () => {
  let bareRepository;
  const tableName = 'RANDOM_TABLE';

  beforeAll(() => {
    bareRepository = new BareRepository(tableName);
    jest.spyOn(ddbClient, 'send').mockImplementation(jest.fn());
  });

  it('should call dynamodb operation: scan', async () => {
    await bareRepository.scan();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(ScanCommand));
  });

  it('should call dynamodb operation: query', async () => {
    await bareRepository.query();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(QueryCommand));
  });

  it('should call dynamodb operation: get', async () => {
    await bareRepository.get();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(GetCommand));
  });

  it('should call dynamodb operation insert via: PutCommand', async () => {
    await bareRepository.save();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(PutCommand));
  });

  it('should call dynamodb operation update via: UpdateCommand', async () => {
    await bareRepository.update();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(UpdateCommand));
  });

  it('should call dynamodb operation delete via: DeleteCommand', async () => {
    await bareRepository.delete();
    expect(ddbClient.send).toHaveBeenCalledWith(expect.any(DeleteCommand));
  });

  it('should build create payload correctly', () => {
    const item = { id: '123', name: 'test' };
    const payload = bareRepository.buildCreatePayload(item);

    expect(payload).toEqual({
      TableName: tableName,
      Item: item
    });
  });

  it('should build update payload correctly with default return values', () => {
    const key = { id: '123' };
    const attributes = { name: 'updated', status: 'active' };

    const payload = bareRepository.buildUpdatePayload(key, attributes);

    expect(payload.TableName).toBe(tableName);
    expect(payload.Key).toEqual(key);
    expect(payload.ReturnValues).toBe(ReturnValues.ALL_NEW);
    expect(payload).toHaveProperty('UpdateExpression');
    expect(payload).toHaveProperty('ExpressionAttributeNames');
    expect(payload).toHaveProperty('ExpressionAttributeValues');
  });

  it('should build update payload with custom return values', () => {
    const key = { id: '123' };
    const attributes = { name: 'updated' };

    const payload = bareRepository.buildUpdatePayload(
      key,
      attributes,
      ReturnValues.UPDATED_OLD
    );

    expect(payload.ReturnValues).toBe(ReturnValues.UPDATED_OLD);
  });

  it('should build delete payload correctly with default return values', () => {
    const key = { id: '123' };
    const attributes = { reason: 'expired' };

    const payload = bareRepository.buildDeletePayload(key, attributes);

    expect(payload.TableName).toBe(tableName);
    expect(payload.Key).toEqual(key);
    expect(payload.ReturnValues).toBe(ReturnValues.ALL_OLD);
    expect(payload).toHaveProperty('UpdateExpression');
    expect(payload).toHaveProperty('ExpressionAttributeNames');
    expect(payload).toHaveProperty('ExpressionAttributeValues');
  });
});
