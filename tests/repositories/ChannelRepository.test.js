const ChannelRepository = require('@repositories/ChannelRepository');
const BareRepository = require('@root/src/repositories/BaseRepository');
const { DDB_TABLE_CHANNEL } = require('@configs/env');
const { ReturnValues } = require('@utils/database');

describe('ChannelRepository', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Singleton Pattern', () => {
    test('should be a singleton instance', () => {
      const instance1 = ChannelRepository;
      const instance2 = require('@repositories/ChannelRepository');
      expect(instance1).toBe(instance2);
    });

    test('getInstance should return the existing instance if already created', () => {
      // Store the original instance
      const originalInstance = ChannelRepository;

      // Access the class constructor through the instance's constructor property
      const ChannelRepositoryClass = originalInstance.constructor;

      // Call getInstance directly
      const newInstance = ChannelRepositoryClass.getInstance();

      // Should return the same instance
      expect(newInstance).toBe(originalInstance);
    });
  });

  describe('Inheritance', () => {
    test('should extend BareRepository', () => {
      expect(ChannelRepository).toBeInstanceOf(BareRepository);
    });

    test('should initialize with the correct table name', () => {
      expect(ChannelRepository.tableName).toBe(DDB_TABLE_CHANNEL);
    });
  });

  describe('Basic Repository Operations', () => {
    test('should have access to inherited methods from BareRepository', () => {
      // Verify that common repository methods are available
      expect(typeof ChannelRepository.get).toBe('function');
      expect(typeof ChannelRepository.save).toBe('function');
      expect(typeof ChannelRepository.update).toBe('function');
      expect(typeof ChannelRepository.delete).toBe('function');
      expect(typeof ChannelRepository.query).toBe('function');
    });
  });

  describe('getChannelById', () => {
    test('should call get with correct parameters', async () => {
      const channelId = 'test-channel';
      const mockResponse = { id: channelId, name: 'Test Channel' };

      // Mock the get method
      ChannelRepository.get = jest.fn().mockResolvedValueOnce(mockResponse);

      const result = await ChannelRepository.getChannelById(channelId);

      expect(ChannelRepository.get).toHaveBeenCalledWith({
        Key: { id: channelId },
        ReturnValues: ReturnValues.ALL_NEW
      });
      expect(result).toEqual(mockResponse);
    });

    test('should propagate errors from get method', async () => {
      const channelId = 'test-channel';
      const error = new Error('Database error');

      // Mock the get method to throw an error
      ChannelRepository.get = jest.fn().mockRejectedValueOnce(error);

      await expect(ChannelRepository.getChannelById(channelId)).rejects.toThrow(
        'Database error'
      );
      expect(ChannelRepository.get).toHaveBeenCalledWith({
        Key: { id: channelId },
        ReturnValues: ReturnValues.ALL_NEW
      });
    });
  });
});
