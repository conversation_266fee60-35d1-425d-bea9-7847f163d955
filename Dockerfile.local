ARG NODE_VERSION=22.14.0
FROM node:$NODE_VERSION-alpine AS base

# Stage 1: Dependencies
FROM base AS deps
WORKDIR /home/<USER>/app

# Copy only the necessary files
COPY package*.json ./
RUN npm ci \
    --verbose \
    --ignore-scripts \
    --omit=dev

# Stage 2: Runtime
FROM base AS runner
# Install runtime dependencies
RUN apk --no-cache add \
    lz4-dev \
    cyrus-sasl \
    ca-certificates

WORKDIR /home/<USER>/app

COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY . .

# Uncomment this if local testing/development
COPY .env.example .env
USER node
ENTRYPOINT ["node", "index.js"]
