const aliases = require('module-alias-jest/register');
/**
 * For a detailed explanation regarding each configuration property, visit:
 * https://jestjs.io/docs/configuration
 */

/** @type {import('jest').Config} */
const config = {
  // Automatically clear mock calls, instances, contexts and results before every test
  clearMocks: true,

  // Indicates whether the coverage information should be collected while executing the test
  collectCoverage: true,

  // An array of glob patterns indicating a set of files for which coverage information should be collected
  collectCoverageFrom: ['src/**/*.js'],

  // The directory where Jest should output its coverage files
  coverageDirectory: 'coverage',

  // An array of regexp pattern strings used to skip coverage collection
  coveragePathIgnorePatterns: ['/node_modules/', '/coverage/'],

  // A list of reporter names that <PERSON><PERSON> uses when writing coverage reports
  coverageReporters: ['text', 'html'],

  // Use this configuration option to add custom reporters to Jest
  reporters: ['default', 'jest-sonar'],

  displayName: {
    name: 'THOUGHT DUMP',
    color: 'blue'
  },

  moduleNameMapper: aliases.jest
};

module.exports = config;
