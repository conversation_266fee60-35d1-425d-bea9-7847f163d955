# Development Container Setup

This directory contains configuration for a development container that closely mirrors the production environment defined in the project's main Dockerfile.

## Features

- Node.js v22.14.0 development environment
- SFTP server for file transfers
- Pre-configured VS Code settings and extensions

## Getting Started

1. Install the [Remote - Containers](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.remote-containers) extension for VS Code.
2. Open the project in VS Code.
3. Click on the green icon in the bottom-left corner of VS Code and select "Reopen in Container".
4. Wait for the container to build and start.

## SFTP Server

The development environment includes an SFTP server for file transfers.

- **Host**: localhost
- **Port**: 2222
- **Username**: sftpuser
- **Password**: password
- **Upload Directory**: /home/<USER>/upload

### Connecting to SFTP

You can connect to the SFTP server using any SFTP client, such as FileZilla, or using the VS Code SFTP extension.

#### Using VS Code SFTP Extension

1. Install the [SFTP extension](https://marketplace.visualstudio.com/items?itemName=liximomo.sftp) for VS Code.
2. Create a new SFTP configuration:

```json
{
    "name": "Dev Container SFTP",
    "host": "localhost",
    "port": 2222,
    "username": "sftpuser",
    "password": "password",
    "remotePath": "/home/<USER>/upload",
    "uploadOnSave": false
}
```

## Notes

- The Node.js application runs as the `node` user for security.
- The SFTP server runs as a separate container.
- Files uploaded to the SFTP server are stored in a Docker volume.