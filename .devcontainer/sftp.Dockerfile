FROM alpine:latest

# Install OpenSSH server
RUN apk add --no-cache openssh openssh-sftp-server

# Configure SSH
RUN mkdir -p /var/run/sshd && \
    mkdir -p /root/.ssh && \
    chmod 700 /root/.ssh && \
    echo "PasswordAuthentication yes" >> /etc/ssh/sshd_config && \
    echo "PermitRootLogin yes" >> /etc/ssh/sshd_config && \
    echo "Subsystem sftp /usr/lib/ssh/sftp-server" >> /etc/ssh/sshd_config

# Create a test user with password
RUN adduser -D -h /home/<USER>/bin/sh sftpuser && \
    echo "sftpuser:password" | chpasswd && \
    mkdir -p /home/<USER>/upload && \
    chown -R sftpuser:sftpuser /home/<USER>

# Generate SSH host keys
RUN ssh-keygen -A

# Expose SSH port
EXPOSE 22

# Start SSH server
CMD ["/usr/sbin/sshd", "-D"]