version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/home/<USER>/app:cached
      - /home/<USER>/app/node_modules
    command: sleep infinity
    environment:
      - NODE_ENV=development
    networks:
      - app-network

  sftp:
    build:
      context: .
      dockerfile: sftp.Dockerfile
    ports:
      - '2222:22'
    volumes:
      - sftp_data:/home/<USER>/upload
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  sftp_data:
