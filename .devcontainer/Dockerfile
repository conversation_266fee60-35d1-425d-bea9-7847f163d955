ARG NODE_VERSION=22.14.0
FROM node:${NODE_VERSION}-alpine AS base

# Install runtime dependencies
RUN apk --no-cache add \
    lz4-dev \
    cyrus-sasl \
    ca-certificates \
    git

# Set the working directory
WORKDIR /home/<USER>/app

RUN mkdir -p node_modules && chown -R node:node node_modules

# Switch to the non-root user
USER node




# # Copy package files
# COPY --chown=node:node package*.json ./

# # Install dependencies
# RUN npm ci \
#     --verbose \
#     --ignore-scripts

# # Copy the rest of the application
# COPY --chown=node:node . .

# # Set up environment
# COPY --chown=node:node .env.example .env

# CMD ["node", "index.js"]