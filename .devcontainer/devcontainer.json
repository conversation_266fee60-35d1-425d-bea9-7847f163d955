{"name": "gpayo-bills-payment-fallout-cron-job", "dockerComposeFile": "docker-compose.yml", "service": "app", "workspaceFolder": "/home/<USER>/app", "customizations": {"vscode": {"extensions": ["dbaeumer.vscode-eslint", "esbenp.prettier-vscode", "Natizyskunk.sftp", "sourcegraph.cody-ai"], "settings": {"editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true}}}}, "forwardPorts": [2222], "postCreateCommand": "npm install", "remoteUser": "node"}