image: $PE_JFROG_URL/hmd-docker-virtual/node:18.18.0-alpine
default:
    tags:
        - $PE_RUNNER_TAG

include:
    - project: globetelecom/common-toolchains/pipeline-coe/pipeline-modules
      ref: v1.1.2
      file:
       - Application.gitlab-ci.yml

stages:
    - test
    - build-image
    - scan-image
    - publish-image
    
semgrep-sast:
    extends: .sast-analyzer
    image:
      name: $PE_JFROG_URL/hmd-gitlab-registry/security-products/semgrep:4
    rules:
        - when: always

nodejs-scan-sast:
    extends: .sast-analyzer
    image:
      name: $PE_JFROG_URL/hmd-gitlab-registry/security-products/nodejs-scan:4
    rules:
        - when: always

build-image:
    stage: build-image
    extends: .docker-build:kaniko
    artifacts:
      paths:
        - app_image.tar
      when: always
    only:
        - tags

scan-image:
    stage: scan-image
    extends: .app-scan:prisma-image-pe
    only:
        - tags

publish-image:
    stage: publish-image
    extends: .docker-publish:jfrog
    only:
        - tags
