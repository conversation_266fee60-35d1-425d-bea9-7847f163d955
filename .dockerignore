# Version control
.git
.gitignore
.gitlab-ci.yml
.gitlab

# Build artifacts and dependencies
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log
dist
build
coverage
*.tgz

# Environment and configuration
.env
*.env
config/*.local.js
config/*.local.json

# IDE and editor files
.idea
.vscode
*.swp
*.swo
*~
.DS_Store

# Documentation
docs
*.md
LICENSE
CHANGELOG.md

# Test files
test
tests
__tests__
*.test.js
*.spec.js
jest.config.js

# Logs
logs
*.log

# Temporary files
tmp
temp
.tmp

# Docker related
Dockerfile*
docker-compose*
.dockerignore

# Miscellaneous
eslint.config.js
.editorconfig
.eslintrc
.eslintignore
.prettierrc
.prettierignore
.babelrc
.nyc_output
.husky
