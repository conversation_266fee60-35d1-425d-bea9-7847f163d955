class LukeFailedPostings {
  constructor({
    entities,
    paymentRepository,
    failedTransactionRepository,
    config,
    services,
    logger,
    utils,
    t,
    S3Client,
    SftpClient,
    batchFileRepository,
    configRepository,
    postPaymentConfigRepository
  }) {
    this.paymentRepository = paymentRepository;
    this.failedTransactionRepository = failedTransactionRepository;
    this.services = services;
    this.config = config;
    this.logger = logger;
    this.utils = utils;
    this.entities = entities;
    this.t = t;
    this.S3Client = S3Client;
    this.SftpClient = SftpClient;
    this.batchFileRepository = batchFileRepository;
    this.configRepository = configRepository;
    this.postPaymentConfigRepository = postPaymentConfigRepository;
  }

  async execute() {
    const { log, FileStream } = this.utils;
    const { lukePost } = this.entities;
    const {
      Luke,
      PaymentMode,
      DepositAccountNumber,
      FalloutFileNameKeys,
      CustomerTypes,
      ConsumerPaymentMethods,
      CorpPaymentMethods
    } = this.t;
    const { rules } = this.entities;

    this.consumerPaymentMethods = ConsumerPaymentMethods;
    this.corpPaymentMethods = CorpPaymentMethods;
    this.customerTypes = CustomerTypes;

    const FalloutAccounts = {
      globe: {},
      innove: {},
      bayan: {}
    };

    this.initFalloutAccounts(FalloutAccounts, FalloutFileNameKeys);

    let failedTransactions = [];

    this.logger.info({
      message: `[START] LukeCron`,
      payload: log(FalloutAccounts)
    });

    /**
     * Get all failed transactions
     */
    try {
      failedTransactions = await this.failedTransactionRepository.list();
      const failedTransactionsID = failedTransactions.map((ft) => ft.id);

      this.logger.info({
        message: `[INFO] Failed Transactions`,
        payload: log(failedTransactionsID),
        count: failedTransactions.length
      });
    } catch (error) {
      this.logger.error({
        message: 'Error getting failed transactions',
        payload: log(error)
      });

      return;
    }

    // If the pgwprd-failedtransactions is empty we cut the execution
    if (failedTransactions === undefined || failedTransactions.length == 0) {
      this.logger.info({
        message: `[INFO] There are no Failed Transactions.`
      });
      return;
    }

    const dateTodayAsiaManila = new Date().toLocaleString('en-US', {
      timeZone: 'Asia/Manila'
    });
    const batchDate = new Date(dateTodayAsiaManila);
    const hours = batchDate.getHours();
    let batchNo;

    if (hours >= 9 && hours < 13) {
      batchNo = '01';
    } else if (hours >= 13 && hours < 17) {
      batchNo = '02';
    } else if (hours >= 17 && hours < 21) {
      batchNo = '03';
    } else {
      batchNo = '04';
    }

    let type, depositAccountNumber;

    const transactionDate = new Date();
    const month = (transactionDate.getMonth() + 1).toString().padStart(2, '0');
    const day = transactionDate.getDate().toString().padStart(2, '0');
    const fullYear = transactionDate.getFullYear();
    const year = transactionDate.getFullYear().toString().slice(2, 4);
    const paybillCorpChannelId = await this.configRepository.get(
      'PAYBILL_CORP_CHANNEL_ID'
    );

    const postTransactions = await Promise.all(
      failedTransactions.map(async (failedTransaction) => {
        const { id, channelId } = failedTransaction;
        const {
          accountName,
          accountNumber,
          amountValue,
          serviceNumber,
          paymentMethod,
          accountType,
          gatewayProcessor
        } = JSON.parse(failedTransaction.state);

        if (gatewayProcessor === 'bpi' || gatewayProcessor === 'xendit') {
          if (this.isGlobe(accountType)) {
            type = 'globe';
          } else if (this.isInnove(accountType)) {
            type = 'innove';
          } else {
            type = 'bayan';
          }
          depositAccountNumber = DepositAccountNumber[gatewayProcessor][type];
        } else {
          //add an if check that handles the typeError encountered to be dynamically handled if depaccountnum is not defined (ticket 11364).
          type =
            this.isGlobe(accountType) || this.isBayan(accountType)
              ? 'globe'
              : 'innove';
          if (
            DepositAccountNumber[paymentMethod] === undefined ||
            typeof DepositAccountNumber[paymentMethod] === 'undefined'
          ) {
            this.logger.info({
              message:
                'Undefined Payment Methods. Will be considered credit card for mapping',
              payload: log(paymentMethod)
            });
            //globe or innove
            depositAccountNumber = type === 'globe' ? ********** : **********;
          } else {
            depositAccountNumber = DepositAccountNumber[paymentMethod][type];
          }
        }

        // put condition for paymentMode
        // if direct debit use channelCode

        let channelPaymentMode;
        try {
          const compositeKey = `${gatewayProcessor}_${paymentMethod}`;
          const postPaymentConfig =
            await this.postPaymentConfigRepository.getPaymentModes(
              channelId,
              compositeKey
            );

          if (postPaymentConfig) {
            channelPaymentMode = postPaymentConfig.billFallout;
          }
          this.logger.info({
            message: '[INFO] Post Payment Configurations',
            payload: {
              postPaymentConfig: log(postPaymentConfig),
              channelPaymentMode: channelPaymentMode
            }
          });
        } catch (error) {
          this.logger.error({
            message: '[ERROR] Cannot fetch PostPayment Configurations',
            payload: log(error)
          });
        }

        const mappedData = {
          transactionDate: month + day + fullYear,
          dealerName: accountName ? accountName.substring(0, 40) : 'N/A',
          customerAccountNo: accountNumber,
          transactionAmount: amountValue,
          depositAccountNumber: depositAccountNumber,
          serviceNumber: serviceNumber,
          receiptingReferenceNumber: id,
          debitCreditIndicator: 'C',
          cashCheckIndicator: '0',
          paymentMode: channelPaymentMode || PaymentMode[paymentMethod]
        };

        const isCorp =
          (process.env.PAYBILL_CORP_CHANNEL_ID || paybillCorpChannelId) ===
          channelId;
        const isCard =
          paymentMethod === 'visa' ||
          paymentMethod === 'mc' ||
          paymentMethod === 'cc' ||
          paymentMethod === 'others' ||
          paymentMethod === 'amex' ||
          paymentMethod === 'jcb' ||
          paymentMethod === 'dropin' ||
          !Object.hasOwn(DepositAccountNumber, 'paymentMethod');

        let falloutAccount;
        if (this.isGlobe(accountType)) {
          falloutAccount = FalloutAccounts['globe'];
        } else if (this.isInnove(accountType)) {
          falloutAccount = FalloutAccounts['innove'];
        } else if (this.isBayan(accountType)) {
          falloutAccount = FalloutAccounts['bayan'];
        }

        let accountTransactions;
        if (isCorp) {
          accountTransactions = falloutAccount.corp;
        } else {
          accountTransactions = falloutAccount.consumer;
        }

        if (isCard) {
          accountTransactions['cc_dc'].mappedData.push(mappedData);
          accountTransactions['cc_dc'].rawData.push(failedTransaction);
          accountTransactions['cc_dc'].ids.push(failedTransaction.id);
        } else if (paymentMethod === 'dragonpay_gcash') {
          accountTransactions['gcash'].mappedData.push(mappedData);
          accountTransactions['gcash'].rawData.push(failedTransaction);
          accountTransactions['gcash'].ids.push(failedTransaction.id);
        } else if (paymentMethod === 'bank') {
          accountTransactions['bank'].mappedData.push(mappedData);
          accountTransactions['bank'].rawData.push(failedTransaction);
          accountTransactions['bank'].ids.push(failedTransaction.id);
        } else if (gatewayProcessor === 'xendit') {
          accountTransactions['xendit'].mappedData.push(mappedData);
          accountTransactions['xendit'].rawData.push(failedTransaction);
          accountTransactions['xendit'].ids.push(failedTransaction.id);
        }

        this.logger.info({
          message: `[INFO] mappedData`,
          payload: log(mappedData)
        });

        return mappedData;
      })
    );

    const { globe, innove, bayan } = FalloutAccounts;

    this.logger.info({
      message: '[INFO] LukeCron: Globe Transactions',
      payload: {
        totalTxnCount: this.getAccountTotalTransactions(globe),
        consumerTxnCount:
          globe.consumer['cc_dc'].ids.length +
          globe.consumer['gcash'].ids.length +
          globe.consumer['bank'].ids.length +
          globe.consumer['xendit'].ids.length,
        corpTxncount:
          globe.corp['cc_dc'].ids.length + globe.corp['gcash'].ids.length,
        consumer: log({
          cc_dc: globe.consumer['cc_dc'].ids,
          gcash: globe.consumer['gcash'].ids,
          bank: globe.consumer['bank'].ids,
          xendit: globe.consumer['xendit'].ids
        }),
        corp: log({
          cc_dc: globe.corp['cc_dc'].ids,
          gcash: globe.corp['gcash'].ids
        })
      }
    });

    this.logger.info({
      message: '[INFO] LukeCron: Innove Transactions',
      payload: {
        totalTxnCount: this.getAccountTotalTransactions(innove),
        consumerTxnCount:
          innove.consumer['cc_dc'].ids.length +
          innove.consumer['gcash'].ids.length +
          innove.consumer['bank'].ids.length +
          innove.consumer['xendit'].ids.length,
        corpTxncount:
          innove.corp['cc_dc'].ids.length + innove.corp['gcash'].ids.length,
        consumer: log({
          cc_dc: innove.consumer['cc_dc'].ids,
          gcash: innove.consumer['gcash'].ids,
          bank: innove.consumer['bank'].ids,
          xendit: innove.consumer['xendit'].ids
        }),
        corp: log({
          cc_dc: innove.corp['cc_dc'].ids,
          gcash: innove.corp['gcash'].ids
        })
      }
    });

    this.logger.info({
      message: '[INFO] LukeCron: Bayan Transactions',
      payload: {
        totalTxnCount: this.getAccountTotalTransactions(bayan),
        consumerTxnCount:
          bayan.consumer['cc_dc'].ids.length +
          bayan.consumer['gcash'].ids.length +
          bayan.consumer['bank'].ids.length +
          bayan.consumer['xendit'].ids.length,
        corpTxncount:
          bayan.corp['cc_dc'].ids.length + bayan.corp['gcash'].ids.length,
        consumer: log({
          cc_dc: bayan.consumer['cc_dc'].ids,
          gcash: bayan.consumer['gcash'].ids,
          bank: bayan.consumer['bank'].ids,
          xendit: bayan.consumer['xendit'].ids
        }),
        corp: log({
          cc_dc: bayan.corp['cc_dc'].ids,
          gcash: bayan.corp['gcash'].ids
        })
      }
    });

    this.logger.info({
      message: 'Done getting transactions',
      payload: log(postTransactions)
    });

    const LukePosting = new Luke({ postTransactions });
    const { errors } = LukePosting.validate();

    if (errors) {
      this.logger.error({
        message: 'Validation error',
        payload: log(errors)
      });
      return;
    }

    const fileStream = new FileStream(rules());
    const accounts = [globe, innove, bayan];

    // initialize streams
    for (let account of accounts) {
      for (let cusType of CustomerTypes) {
        let paymentMethods;
        if (cusType === 'consumer') paymentMethods = ConsumerPaymentMethods;
        else paymentMethods = CorpPaymentMethods;

        for (let method of paymentMethods) {
          const refObj = account[cusType][method];
          if (refObj.mappedData.length > 0) {
            refObj.stream = fileStream.create(refObj.mappedData);
          }
        }
      }
    }

    try {
      for (let account of accounts) {
        for (let cusType of CustomerTypes) {
          let paymentMethods;
          if (cusType === 'consumer') paymentMethods = ConsumerPaymentMethods;
          else paymentMethods = CorpPaymentMethods;

          for (let method of paymentMethods) {
            const { key, stream, rawData } = account[cusType][method];

            if (stream) {
              let fileName;
              // let fileKey;
              if (cusType === 'consumer') {
                // fileKey = key;
                fileName = `${key + month + day}.${year + batchNo}`;
              } else {
                // fileKey = corpKey;
                fileName = `${key + month + day}.${year + batchNo}crp.txt`;
              }

              await this.processDumping(fileName, stream, rawData);
            }
          }
        }
      }

      this.logger.info({
        message: 'Done: File transfer to luke',
        payload: {}
      });

      this.SftpClient.endConnection();

      this.logger.info({
        message: 'End connection: File transfer to luke',
        payload: {}
      });
    } catch (error) {
      this.logger.error({
        message: 'Error in saving luke file',
        payload: log(error)
      });

      return;
    }

    await this.deleteFailedTransactions(failedTransactions);

    this.logger.info({
      message: 'Creating POSTED_LUKE events',
      payload: {}
    });

    /**
     * Create LukePosted events per transaction and save state
     */
    let promises = [];
    let eventReference;
    failedTransactions.forEach(async (failedTransaction) => {
      if (failedTransaction.retry !== true || failedTransaction.retry !== 1) {
        const saveTransactionPromise = new Promise(async (resolve, reject) => {
          const state = JSON.parse(failedTransaction.state);
          if (state.gatewayProcessor === 'gcash') {
            eventReference = '0';
          } else if (state.gatewayProcessor === 'ipay88') {
            eventReference = '2';
          } else if (state.gatewayProcessor === 'bpi') {
            eventReference = '3';
          } else if (state.gatewayProcessor === 'xendit') {
            eventReference = '4';
          } else {
            eventReference = state.eventReference;
          }

          try {
            const { save } = await this.paymentRepository.getById(
              failedTransaction.id,
              state.gatewayProcessor,
              eventReference
            );
            const lukePostedEvent = lukePost(failedTransaction);
            await save(lukePostedEvent);
            await save([], true);
            resolve(lukePostedEvent);
          } catch (error) {
            reject(error);
          }
        });
        promises.push(saveTransactionPromise);
      }
    });

    try {
      await Promise.all(promises);
      this.logger.info({
        message: 'Done: POSTED_LUKE events',
        payload: {}
      });
    } catch (error) {
      this.logger.error({
        message: 'Error saving luke posted events',
        payload: log(error)
      });
    }

    this.logger.info({
      message: 'END PROCESS',
      payload: {}
    });
    return;
  }

  isGlobe(accountType) {
    return accountType === 'G' || accountType === 'I';
  }

  isInnove(accountType) {
    return accountType === 'N';
  }

  isBayan(accountType) {
    return accountType === 'B';
  }

  async deleteFailedTransactions(failedTransactions) {
    /**
     * Delete all failed transactions
     */
    this.logger.info({
      message: 'Deleting failed transactions',
      payload: {}
    });
    let deleteTransactionPromises = [];

    failedTransactions.forEach(({ id, transactionId, retry }) => {
      const deleteTransactionPromise = new Promise(async (resolve, reject) => {
        try {
          // We delete the transaction if the 'retry' column is not equals to 1.
          if (retry !== true || retry !== 1) {
            this.logger.info({
              message: `Deleted: ${id} : ${transactionId}`,
              payload: {}
            });
            await this.failedTransactionRepository.delete({
              id,
              transactionId
            });
          } else {
            this.logger.info({
              message: 'Skipping to delete the transaction',
              payload: {
                id: id,
                transactionId: transactionId
              }
            });
          }
          resolve({ id, transactionId });
        } catch (error) {
          reject(error);
        }
      });

      deleteTransactionPromises.push(deleteTransactionPromise);
    });

    try {
      await Promise.all(deleteTransactionPromises);
      this.logger.info({
        message: 'Done: Deleting failed transactions',
        payload: {}
      });
    } catch (error) {
      this.logger.error({
        message: 'Error deleting failed transaction logs',
        payload: {}
      });
    }
  }
  // checks if the key == 0 or item.isExisting == 0
  isForDumping(key, failedTransactions) {
    return new Promise(async (resolve, reject) => {
      let toDump = false;

      // Check if the batch file is amenable for dumping.
      await failedTransactions.forEach((item) => {
        if (
          (item[key] && item[key] == 1) ||
          (item.isExisting && item.isExisting != 1) ||
          !item.isExisting
        ) {
          toDump = true;
        }
      });
      if (toDump) {
        return resolve(true);
      }

      return reject(false);
    });
  }

  async processDumping(fileName, fileStream, failedTransactions) {
    const { log } = this.utils;
    try {
      const uploadedS3File = await this.uploadBatchToS3(fileName, fileStream);
      const s3File = await this.readBatchFileFromS3(uploadedS3File);
      await this.dumpBatchFileToSftp(fileName, s3File);
    } catch (error) {
      this.logger.error({
        message: `[ERROR] processDumping(): Failed to Dump - ${fileName}`,
        error: log(error)
      });

      failedTransactions.map((failedTransaction) => {
        failedTransaction.retry = true;
      });
    }
  }

  generateAndDump(filename, fileStream, failedTransactions, filenameKey) {
    return new Promise(async (resolve, reject) => {
      const { log } = this.utils;
      try {
        const uploadedS3File = await this.uploadBatchToS3(filename, fileStream);
        // const s3File = await this.readBatchFileFromS3(uploadedS3File);
        const s3FileStream = await this.readBatchFileFromS3(uploadedS3File);

        // await this.dumpBatchFileToSftp(filename, s3File);
        await this.dumpBatchFileToSftp(filename, s3FileStream);

        // We update each of the transactions fields if a specific batch file is successfully dumped into S3 and SFTP client,
        // to tell our dumping function to skip the dumping of the specific batch file if it has '0', null, or undefined value.
        await Promise.all(
          failedTransactions.map(async (item) => {
            const key = {
              id: item.id,
              transactionId: item.transactionId
            };
            const updateExpression =
              'set accountNumber = :existingVal1, channelId = :existingVal2, #state = :existingVal3, #status = :existingVal4, #timestamp = :existingVal5, #filename = :val1, #retry = :val2, #isExisting = :val3';
            const expressionAttributeNames = {
              '#filename': filenameKey,
              '#retry': 'retry',
              '#isExisting': 'isExisting',
              '#state': 'state',
              '#status': 'status',
              '#timestamp': 'timestamp'
            };
            const expressionAttributeValues = {
              ':existingVal1': item.accountNumber,
              ':existingVal2': item.channelId,
              ':existingVal3': item.state,
              ':existingVal4': item.status,
              ':existingVal5': item.timestamp,
              ':val1': 0,
              ':val2': 0,
              ':val3': 1
            };

            return await this.failedTransactionRepository.update(
              key,
              updateExpression,
              expressionAttributeNames,
              expressionAttributeValues
            );
          })
        ).then(() => {
          this.logger.info({
            message: `${filename} - successfully dumped`,
            payload: {}
          });
          resolve(true);
        });
      } catch (error) {
        this.logger.error({
          message: `Error generateAndDump() - ${filename}`,
          payload: log(error)
        });

        // Put a retry indicator to the transaction items in the dynamodb table when the dumping process fails.
        await Promise.all(
          failedTransactions.map(async (item) => {
            const key = {
              id: item.id,
              transactionId: item.transactionId
            };
            const updateExpression =
              'set accountNumber = :existingVal1, channelId = :existingVal2, #state = :existingVal3, #status = :existingVal4, #timestamp = :existingVal5, #filename = :val1, #retry = :val2, #isExisting = :val3';
            const expressionAttributeNames = {
              '#filename': filenameKey,
              '#retry': 'retry',
              '#state': 'state',
              '#status': 'status',
              '#timestamp': 'timestamp',
              '#isExisting': 'isExisting'
            };
            const expressionAttributeValues = {
              ':existingVal1': item.accountNumber,
              ':existingVal2': item.channelId,
              ':existingVal3': item.state,
              ':existingVal4': item.status,
              ':existingVal5': item.timestamp,
              ':val1': 1,
              ':val2': 1,
              ':val3': 1
            };

            return await this.failedTransactionRepository.update(
              key,
              updateExpression,
              expressionAttributeNames,
              expressionAttributeValues
            );
          })
        ).then(() => {
          reject(false);
        });
      }
    });
  }

  uploadBatchToS3(filename, fileStream) {
    return new Promise(async (resolve, reject) => {
      const { encryptPgp } = this.utils;
      try {
        const encryptedStream = await encryptPgp(fileStream);
        await this.batchFileRepository.saveBatchFiles(filename);
        const uploadedS3File = await this.S3Client.write(
          `cumulus/${filename}`,
          encryptedStream
        );
        resolve(uploadedS3File);
      } catch (error) {
        reject(error);
      }
    });
  }

  readBatchFileFromS3(uploadedS3File) {
    return new Promise(async (resolve, reject) => {
      try {
        // const s3File = this.S3Client.read(uploadedS3File.key);
        // const s3FileStream = await this.S3Client.read(uploadedS3File.key);
        const s3FileStream = await this.S3Client.read(uploadedS3File.Key);
        // resolve(s3File);
        resolve(s3FileStream);
      } catch (error) {
        reject(error);
      }
    });
  }

  dumpBatchFileToSftp(filename, s3FileStream /* s3File */) {
    return new Promise(async (resolve, reject) => {
      const { log } = this.utils;
      if (process.env.NODE_ENV === 'prod') {
        try {
          // const s3FileStream = s3File.createReadStream();
          const targetRemotePath = `${process.env.LUKE_SFTP_UPLOAD_PATH}${filename}`;
          const sftpResponse = await this.SftpClient.upload(
            s3FileStream,
            targetRemotePath
          );

          this.logger.info({
            message: 'SFTP response - ' + filename,
            payload: log(sftpResponse)
          });
        } catch (error) {
          this.logger.error({
            message:
              '[LukeFailedPosting] Error dumping file in sftp - ' + filename,
            payload: log(error)
          });
          reject(error);
        }
      }
      resolve(true);
    });
  }

  initFalloutAccounts(accounts, fileNameKeys) {
    const customerTypes = this.customerTypes;
    const consumerPaymentMethods = this.consumerPaymentMethods;
    const corpPaymentMethods = this.corpPaymentMethods;
    const accountKeys = Object.keys(accounts);

    accountKeys.map((account) => {
      customerTypes.map((cusType) => {
        accounts[account][cusType] = {};

        let paymentMethod = [];
        if (cusType === 'consumer') paymentMethod = consumerPaymentMethods;
        else paymentMethod = corpPaymentMethods;

        paymentMethod.map((method) => {
          accounts[account][cusType][method] = {
            key: fileNameKeys[account][method],
            corpKey:
              cusType === 'corp'
                ? fileNameKeys[account][method] + 'txt'
                : undefined,
            stream: null,
            ids: [],
            rawData: [],
            mappedData: []
          };
        });
      });
    });
  }

  getAccountTotalTransactions(account) {
    const customerTypes = this.customerTypes;
    const consumerPaymentMethods = this.consumerPaymentMethods;
    const corpPaymentMethods = this.corpPaymentMethods;
    let count = 0;

    customerTypes.map((cusType) => {
      let paymentMethods;
      if (cusType === 'consumer') paymentMethods = consumerPaymentMethods;
      else paymentMethods = corpPaymentMethods;

      paymentMethods.map((method) => {
        const mappedData = account[cusType][method].mappedData;
        count += mappedData.length;
      });
    });

    return count;
  }
}

module.exports = LukeFailedPostings;
