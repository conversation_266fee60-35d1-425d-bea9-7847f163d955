const { DDB_TABLE_EVENT_LOGS } = require('@configs/env');
const BaseRepository = require('./BaseRepository');

class EventLogsRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DDB_TABLE_EVENT_LOGS);
    EventLogsRepository.instance = this;
  }

  static getInstance() {
    if (!EventLogsRepository.instance) {
      EventLogsRepository.instance = new EventLogsRepository();
    }
    return EventLogsRepository.instance;
  }
}

module.exports = EventLogsRepository.getInstance();
