const { DDB_TABLE_FAILED_TRANS } = require('@configs/env');
const BaseRepository = require('./BaseRepository');

class FailedTransactionsRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DDB_TABLE_FAILED_TRANS);
    FailedTransactionsRepository.instance = this;
  }

  static getInstance() {
    if (!FailedTransactionsRepository.instance) {
      FailedTransactionsRepository.instance =
        new FailedTransactionsRepository();
    }
    return FailedTransactionsRepository.instance;
  }

  async getFailedPostings(lastEvaluatedKey = null, limit = 1000) {
    const statusToFind = 'POSTING_FAILED';
    const queryParams = {
      IndexName: 'StatusIndex',
      KeyConditionExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': statusToFind
      },
      ScanIndexForward: true, // Sort by createDateTime ascending (earliest first)
      Limit: limit
    };

    if (lastEvaluatedKey) {
      queryParams.ExclusiveStartKey = lastEvaluatedKey;
    }

    const result = await this.query(queryParams);

    return {
      items: result.Items || [],
      count: result.Count,
      lastEvaluatedKey: result.LastEvaluatedKey
    };
  }
}

module.exports = FailedTransactionsRepository.getInstance();
