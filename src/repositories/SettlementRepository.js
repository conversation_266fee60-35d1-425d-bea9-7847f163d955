const { DDB_TABLE_SETTLEMENTS } = require('@configs/env');
const BaseRepository = require('./BaseRepository');

class SettlementRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DDB_TABLE_SETTLEMENTS);
    SettlementRepository.instance = this;
  }

  static getInstance() {
    if (!SettlementRepository.instance) {
      SettlementRepository.instance = new SettlementRepository();
    }
    return SettlementRepository.instance;
  }

  async getFailedPostings(lastEvaluatedKey = null, limit = 1000) {
    const statusToFind = 'POSTING_FAILED';
    const queryParams = {
      IndexName: 'StatusIndex',
      KeyConditionExpression: '#status = :status',
      ExpressionAttributeNames: {
        '#status': 'status'
      },
      ExpressionAttributeValues: {
        ':status': statusToFind
      },
      ScanIndexForward: true, // Sort by createDateTime ascending (earliest first)
      Limit: limit
    };

    if (lastEvaluatedKey) {
      queryParams.ExclusiveStartKey = lastEvaluatedKey;
    }

    const result = await this.query(queryParams);

    return {
      items: result.Items || [],
      count: result.Count,
      lastEvaluatedKey: result.LastEvaluatedKey
    };
  }
}

module.exports = SettlementRepository.getInstance();
