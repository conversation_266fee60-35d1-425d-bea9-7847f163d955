const {
    DDB_TABLE_TRANSACTION_LOGS,
    DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX
  } = require('@configs/env');
  const BaseRepository = require('./BaseRepository');
  class TransactionLogsRepository extends BaseRepository {
    static instance;

    constructor() {
      super(DDB_TABLE_TRANSACTION_LOGS);
      TransactionLogsRepository.instance = this;
    }

    static createInstance() {
      if (!TransactionLogsRepository.instance) {
        TransactionLogsRepository.instance = new TransactionLogsRepository();
      }
      return TransactionLogsRepository.instance;
    }

    async getAll(limit = 1000, lastEvaluatedKey = null) {
      const scanParams = {
        Limit: limit
      };

      if (lastEvaluatedKey) {
        scanParams.ExclusiveStartKey = lastEvaluatedKey;
      }

      const result = await this.scan(scanParams);

      return {
        items: result.Items || [],
        count: result.Count,
        lastEvaluatedKey: result.LastEvaluatedKey
      };
    }

    async getByPaymentId(paymentId) {
      const queryParams = {
        KeyConditionExpression: '#paymentId = :paymentId',
        ExpressionAttributeNames: {
          '#paymentId': 'paymentId'
        },
        ExpressionAttributeValues: {
          ':paymentId': paymentId
        }
      };

      const result = await this.query(queryParams);

      return result.Items || [];
    }
  }
  
  module.exports = TransactionLogsRepository.createInstance();
  