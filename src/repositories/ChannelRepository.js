const BaseRepository = require('./BaseRepository');
const { DDB_TABLE_CHANNEL } = require('@configs/env');
const { ReturnValues } = require('@utils/database');

class ChannelRepository extends BaseRepository {
  static instance;
  constructor() {
    super(DDB_TABLE_CHANNEL);
    ChannelRepository.instance = this;
  }

  async getChannelById(channelId) {
    return await this.get({
      Key: { id: channelId },
      ReturnValues: ReturnValues.ALL_NEW
    });
  }

  static getInstance() {
    if (!ChannelRepository.instance) {
      ChannelRepository.instance = new ChannelRepository();
    }
    return ChannelRepository.instance;
  }
}

module.exports = ChannelRepository.getInstance();
