// DDB Client
const ddbClient = require('@configs/ddb');
const {
  DeleteCommand,
  GetCommand,
  PutCommand,
  QueryCommand,
  ScanCommand,
  UpdateCommand
} = require('@aws-sdk/lib-dynamodb');
const { transformJsonAttributes, ReturnValues } = require('@utils/database');

class BaseRepository {
  constructor(tableName) {
    this.ddbClient = ddbClient;
    this.tableName = tableName;
  }

  async scan(params = {}) {
    return this.ddbClient.send(
      new ScanCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async query(params = {}) {
    return this.ddbClient.send(
      new QueryCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async get(params = {}) {
    return this.ddbClient.send(
      new GetCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async save(params = {}) {
    return this.ddbClient.send(
      new PutCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async update(params = {}) {
    return this.ddbClient.send(
      new UpdateCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  async delete(params = {}) {
    return this.ddbClient.send(
      new DeleteCommand({
        TableName: this.tableName,
        ...params
      })
    );
  }

  buildCreatePayload(item) {
    return {
      TableName: this.tableName,
      Item: item
    };
  }

  buildUpdatePayload(key, attributes, returnValues = ReturnValues.ALL_NEW) {
    return {
      TableName: this.tableName,
      Key: key,
      ...transformJsonAttributes(attributes, returnValues)
    };
  }

  buildDeletePayload(key, attributes, returnValues = ReturnValues.ALL_OLD) {
    return {
      TableName: this.tableName,
      Key: key,
      ...transformJsonAttributes(attributes, returnValues)
    };
  }
}

module.exports = BaseRepository;
