const chunkArray = (array, chunkSize) => {
  if (chunkSize <= 0) {
    throw new Error('Chunk size must be greater than 0');
  }
  if (!Array.isArray(array)) {
    throw new Error('First argument must be an array');
  }
  if (chunkSize >= array.length) {
    return [array];
  }

  const chunks = [];
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

module.exports = {
  chunkArray
};
