const XENDIT_STATUS_MAPPING = {
    ACTIVE: 'ECPAY_GENERATED',
    COMPLETED: 'ECPAY_AUTHORISED',
    INACTIVE: 'ECPAY_DROPPED',
    EXPIRED: 'ECPAY_EXPIRED'
  };
  
  const getStatus = (status) => {
    const s = XENDIT_STATUS_MAPPING[status];
    if (s) {
      return s;
    }
    throw new Error(`Invalid status: ${status}`);
  };
  
  const paymentProcessedPayload = ({
    paymentId,
    status,
    paymentCode,
    expiry
  }) => {
    const payloadBody = {
      notification: {
        name: 'PaymentProcessed',
        payload: {
          paymentId,
          accounts: [
            {
              status,
              paymentCode,
              expiry
            }
          ]
        }
      }
    };
    return payloadBody;
  };
  
  module.exports = {
    paymentProcessedPayload,
    getStatus
  };
  