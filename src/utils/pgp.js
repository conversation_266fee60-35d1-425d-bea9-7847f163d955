const { existsSync, readFileSync } = require('fs');
const openpgp = require('openpgp');
const { Readable } = require('stream');
const { PGP_PUBLIC_KEY_PATH } = require('@configs/env');

const pgpEncryptStream = async (fileStream) => {
  const path = PGP_PUBLIC_KEY_PATH;

  const publicKeyArmored = existsSync(path)
    ? `${readFileSync(path).toString()}`
    : null;

  if (!publicKeyArmored) {
    throw new Error('[PGP ERROR] Public key not found');
  }

  const webStream = Readable.toWeb(fileStream);
  const publicKey = await openpgp.readKey({ armoredKey: publicKeyArmored });
  const message = await openpgp.createMessage({ binary: webStream });

  const encrypted = await openpgp.encrypt({
    message,
    encryptionKeys: publicKey,
    config: {
      rejectPublicKeyAlgorithms: new Set(),
      minRSABits: 0,
      allowMissingKeyFlags: true
    }
  });

  return encrypted;
};

module.exports = {
  pgpEncryptStream
};
