const envSchema = require('env-schema');

const schema = {
  type: 'object',
  properties: {
    DDB_AWS_REGION: {
      type: 'string',
      default: 'ap-southeast-1'
    },
    DDB_ENDPOINT: {
      type: 'string',
      default: 'http://localhost:4566' // localstack endpoint
    },
    DDB_MAX_ATTEMPTS: {
      type: 'integer',
      default: 3
    },
    NODE_ENV: {
      type: 'string',
      default: 'local'
    }
  }
};

module.exports = envSchema({
  dotenv: true,
  schema
});