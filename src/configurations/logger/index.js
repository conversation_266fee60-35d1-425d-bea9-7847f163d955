const pino = require('pino');
const env = require('@configs/env');

const baseConfig = {
  level: env.LOG_LEVEL,
  serializers: {
    pid: () => undefined,
    hostname: () => undefined
  }
};

const transportConfigs = {
  local: {
    transport: { target: 'pino-pretty' }
  }
};

const logger = pino({
  ...baseConfig,
  ...(transportConfigs[env.NODE_ENV] || transportConfigs.local)
});

module.exports = logger;
