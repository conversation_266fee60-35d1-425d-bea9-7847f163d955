const {
  stringTypeDefault,
  stringType,
  booleanTypeDefault,
  intTypeDefault
} = require('./util');

const dynamoDBConfigs = {
  DDB_ENABLE_LOGGING: booleanTypeDefault(true),
  DDB_ENDPOINT: stringType,
  DDB_TABLE_TRANSACTION_LOGS: stringType,
  DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX: stringType,
  DDB_TABLE_EVENT_LOGS: stringType,
  DDB_TABLE_CHANNEL: stringType,
  DDB_TABLE_SETTLEMENTS: stringType,
  DDB_TABLE_FAILED_TRANS: stringType
};

const sftpConfigs = {
  SFTP_HOST: stringType,
  SFTP_PORT: intTypeDefault(22),
  SFTP_USERNAME: stringType,
  SFTP_PRIVATE_KEY_PATH: stringType
};

const schema = {
  type: 'object',
  required: [
    'DDB_TABLE_TRANSACTION_LOGS',
    'DDB_TABLE_EVENT_LOGS',
    'DDB_TABLE_CHANNEL',
    'DDB_TABLE_SETTLEMENTS',
    'DDB_TABLE_FAILED_TRANS',
    'PGP_PUBLIC_KEY_PATH',
    'FALLOUT_TARGET_PATH',
    ...Object.keys(sftpConfigs)
  ],
  properties: {
    NODE_ENV: stringTypeDefault('local'),
    LOG_LEVEL: stringTypeDefault('info'),
    CXS_CALLBACK_API_KEY: stringType,
    PGP_PUBLIC_KEY_PATH: stringType,
    FALLOUT_TARGET_PATH: stringType,
    ...dynamoDBConfigs,
    ...sftpConfigs
  }
};

module.exports = schema;
