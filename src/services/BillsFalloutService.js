const { name } = require('@root/package.json');
const logger = require('@configs/logger');
const {
  eventLogsRepository,
  settlementRepository,
  transactionLogsRepository,
  failedTransactionsRepository
} = require('@repositories');
const { writeDDBTransaction } = require('@utils/database');
const { createFalloutFileStream } = require('@utils/fileStream');
const { pgpEncryptStream } = require('@utils/pgp');
const { getPhDateTimeString } = require('@utils/date');
const {
  ACCOUNT_TYPE_MAPPING,
  PAYMENT_MODES,
  DEPOSIT_ACCOUNT_NUMBERS,
  getFalloutAccount,
  getCustomerType
} = require('@utils/fallout');
const { generateUuidv7 } = require('@utils/uuid');
const {
  formatDateForFilename,
  formatDateForTransaction
} = require('@utils/date');
const SftpClient = require('@services/SftpService');
const { FALLOUT_TARGET_PATH } = require('@configs/env');

class BillsFalloutService {
  static instance;
  constructor() {
    BillsFalloutService.instance = this;
    this.CHUNK_SIZE = 10; // DynamoDB TransactWrite limit
    this.BATCH_SIZE = 100; // Processing batch size
  }

  static getInstance() {
    if (!BillsFalloutService.instance) {
      BillsFalloutService.instance = new BillsFalloutService();
    }
    return BillsFalloutService.instance;
  }

  async execute() {
    try {
      logger.info('[START] Bills Fallout Processing');

      // Step 1: Fetch failed transactions
      const failedTransactions = await this.getFailedTransactions();

      if (!failedTransactions || failedTransactions.length === 0) {
        logger.info('[INFO] No failed transactions found');
        return { success: true, processed: 0 };
      }

      logger.info(
        `[INFO] Found ${failedTransactions.length} failed transactions`
      );

      // Step 2: Process transactions by account type
      const processedAccounts =
        await this.processTransactionsByAccount(failedTransactions);

      // Step 3: Generate and upload fallout files
      const uploadResults =
        await this.generateAndUploadFiles(processedAccounts);

      // Step 4: Update transaction statuses (parallel with chunking)
      await this.updateTransactionStatuses(uploadResults);

      logger.info('[END] Bills Fallout Processing completed successfully');
      return {
        success: true,
        processed: failedTransactions.length,
        uploadResults
      };
    } catch (error) {
      logger.error('[ERROR] Bills Fallout Processing failed', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  async getFailedTransactions() {
    const failedTransactions = [];

    let lastEvaluatedKey = null;
    let count = 0;
    try {
      do {
        const {
          items,
          count: batchCount,
          lastEvaluatedKey: key
        } = await failedTransactionsRepository.getFailedPostings(
          lastEvaluatedKey
        );

        failedTransactions.push(...items);
        count += batchCount;
        lastEvaluatedKey = key;
      } while (lastEvaluatedKey);
    } catch (error) {
      logger.error('[ERROR] Bills Fallout Fetching Failed Transactions', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }

    return failedTransactions;
  }

  async processTransactionsByAccount(failedTransactions) {
    try {
      logger.info('[INFO] Processing transactions by account type');

      const falloutAccountMap = {};

      for (const transaction of failedTransactions) {
        const {
          // From translogs, maybe add on failedTrans for flattening
          // paymentMethod,
          // gatewayProcessor,
          paymentId,
          channelId,
          accountNumber,
          amountValue,
          accountType,
          // saved on failedTrans but fetched from ESB GetAccountInfo
          serviceNumber,
          accountName
        } = transaction;

        // TODO: CREATE TRANSLOG CACHE
        const [{ paymentMethod, gatewayProcessor }] =
          await transactionLogsRepository.getByPaymentId(paymentId);

        const accountTypeKey = ACCOUNT_TYPE_MAPPING[accountType] || 'globe';
        // we have `card_straight` and `card_installment` payment methods
        // we just categorize them to `card` since they will be on the same fallout file
        const paymentMethodKey = paymentMethod.includes('card')
          ? 'card'
          : paymentMethod;
        const customerType = getCustomerType(channelId);

        const { success, message, falloutAccount } = getFalloutAccount(
          falloutAccountMap,
          {
            accountType: accountTypeKey,
            customerType,
            paymentMethod: paymentMethodKey
          }
        );

        if (!success) {
          logger.error('[ERROR] Failed to get fallout account', {
            error: message
          });
          continue;
        }

        falloutAccount.ids.push(paymentId);
        falloutAccount.rawData.push(transaction);

        const mappedData = this.mapTransactionData({
          paymentId,
          accountNumber,
          accountType: accountTypeKey,
          // we still use paymentMethod in case `card_straight` and `card_installment`
          // will need different DepositAccountNumber/PaymentMode
          paymentMethod,
          amountValue,
          serviceNumber,
          accountName
        });
        falloutAccount.mappedData.push(mappedData);
      }

      return falloutAccountMap;
    } catch (error) {
      logger.error('[ERROR] Failed to process transactions by account', {
        error: error.message
      });
      throw error;
    }
  }

  async generateAndUploadFiles(accounts) {
    try {
      logger.info('[INFO] Generating and uploading fallout files');

      const uploadResults = [];
      const currentDate = new Date();
      const dateStr = formatDateForFilename(currentDate);

      for (const [key, account] of Object.entries(accounts)) {
        if (account.mappedData.length === 0) {
          continue;
        }

        const [accountType, customerType, paymentMethod] = key.split('-');
        const filename = this.generateFilename(
          account.key,
          customerType,
          dateStr
        );

        const fileStream = createFalloutFileStream(account.mappedData);

        const uploadResult = await this.uploadToSftp(filename, fileStream);

        uploadResults.push({
          accountType,
          customerType,
          paymentMethod,
          filename,
          transactionCount: account.mappedData.length,
          success: uploadResult.success,
          rawData: account.rawData
        });

        logger.info(
          `[INFO] Processed ${filename}: ${account.mappedData.length} transactions`
        );
      }

      return uploadResults;
    } catch (error) {
      logger.error('[ERROR] Failed to generate and upload files', {
        error: error.message
      });
      throw error;
    }
  }

  async uploadToSftp(filename, fileStream) {
    try {
      logger.info(`[INFO] Uploading ${filename} to SFTP`);
      const encryptedStream = await pgpEncryptStream(fileStream);

      const { Readable } = require('stream');

      // pgp stream is now WebStream so we need to convert it to ReadableStream
      const readableStream = Readable.from(encryptedStream);
      const targetRemotePath = `${FALLOUT_TARGET_PATH}/${filename}`;
      await SftpClient.upload(readableStream, targetRemotePath);

      return { success: true, filename };
    } catch (error) {
      logger.error(`[ERROR] Failed to upload ${filename} to SFTP`, {
        error: error.message
      });
      throw error;
    }
  }

  async updateTransactionStatuses(uploadResults) {
    try {
      logger.info('[INFO] Updating transaction statuses');

      // Filter successful uploads
      const successfulUploads = uploadResults.filter((ur) => ur.success);

      if (successfulUploads.length === 0) {
        logger.warn('[WARN] No successful uploads to update');
        return;
      }

      const updatePromises = [];
      for (const upload of successfulUploads) {
        const chunks = this.chunkArray(upload.rawData, this.CHUNK_SIZE);
        const chunksPromises = chunks.map((chunk) =>
          this.updateTransactionChunk(upload.filename, chunk)
        );
        updatePromises.push(...chunksPromises);
      }

      await Promise.all(updatePromises);

      logger.info('[INFO] Successfully updated all transaction statuses');
    } catch (error) {
      logger.error('[ERROR] Failed to update transaction statuses', {
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Update a chunk of transactions using TransactWrite
   */
  async updateTransactionChunk(filename, transactionsArray) {
    try {
      const phTimeNow = getPhDateTimeString();
      const operations = [];

      for (const transaction of transactionsArray) {
        const settlementParams = settlementRepository.buildUpdatePayload(
          {
            paymentId: transaction.paymentId,
            transactionId: transaction.transactionId
          },
          { status: 'POSTED_BATCHFILE', updateDateTime: phTimeNow }
        );

        const failedTransactionsParams =
          failedTransactionsRepository.buildUpdatePayload(
            {
              paymentId: transaction.paymentId,
              transactionId: transaction.transactionId,
              batchFileName: filename
            },
            { status: 'POSTED_BATCHFILE', updateDateTime: phTimeNow }
          );

        const eventLogsParams = eventLogsRepository.buildCreatePayload({
          paymentId: transaction.paymentId,
          eventId: generateUuidv7(),
          eventName: 'POSTING_BATCHFILE',
          eventSource: name,
          eventStatus: 'SUCCESS_POSTING_BATCHFILE',
          eventDetails: transaction,
          createDateTime: phTimeNow
        });

        operations.push(
          ...[
            {
              operation: 'Update',
              params: settlementParams
            },
            {
              operation: 'Update',
              params: failedTransactionsParams
            },
            {
              operation: 'Put',
              params: eventLogsParams
            }
          ]
        );
      }

      await writeDDBTransaction(operations);

      logger.info(
        `[INFO] Updated chunk of ${transactionsArray.length} transactions`
      );
    } catch (error) {
      logger.error('[ERROR] Failed to update transaction chunk', {
        error: error.message,
        transactionIds: transactionsArray.map((t) => t.transactionId)
      });
      throw error;
    }
  }

  mapTransactionData({
    paymentId,
    accountNumber,
    accountType,
    paymentMethod,
    amountValue,
    serviceNumber,
    accountName
  }) {
    const currentDate = new Date();
    const transactionDate = formatDateForTransaction(currentDate);

    return {
      transactionDate,
      dealerName: accountName ? accountName.substring(0, 40) : 'N/A',
      customerAccountNo: accountNumber,
      transactionAmount: amountValue,
      depositAccountNumber: this.getDepositAccountNumber(
        accountType,
        paymentMethod
      ),
      serviceNumber,
      receiptingReferenceNumber: paymentId,
      debitCreditIndicator: 'C',
      cashCheckIndicator: '0',
      paymentMode: this.getPaymentMode(paymentMethod)
    };
  }

  //Get deposit account number based on account type and payment method
  getDepositAccountNumber(accountType, paymentMethod) {
    if (
      DEPOSIT_ACCOUNT_NUMBERS[paymentMethod] &&
      DEPOSIT_ACCOUNT_NUMBERS[paymentMethod][accountType]
    ) {
      return DEPOSIT_ACCOUNT_NUMBERS[paymentMethod][accountType];
    }
    return (
      DEPOSIT_ACCOUNT_NUMBERS.default[accountType] ||
      DEPOSIT_ACCOUNT_NUMBERS.default.globe
    );
  }

  getPaymentMode(paymentMethod) {
    return PAYMENT_MODES[paymentMethod] || PAYMENT_MODES.card;
  }

  generateFilename(key, customerType) {
    const dateTodayAsiaManila = new Date().toLocaleString('en-US', {
      timeZone: 'Asia/Manila'
    });
    const manilaDate = new Date(dateTodayAsiaManila);
    const dateStr = formatDateForFilename(manilaDate);
    const hours = manilaDate.getHours();
    let batchNo;

    // 9am - 12:59pm
    if (hours >= 9 && hours < 13) {
      batchNo = '01';
      // 1pm - 4:59pm
    } else if (hours >= 13 && hours < 17) {
      batchNo = '02';
      // 5pm - 8:59pm
    } else if (hours >= 17 && hours < 21) {
      batchNo = '03';
      // 9pm - 8:59am
    } else {
      batchNo = '04';
    }

    if (customerType === 'corp') {
      return `${key}${dateStr}${batchNo}crp.txt`;
    } else {
      return `${key}${dateStr}${batchNo}`;
    }
  }



  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

module.exports = BillsFalloutService.getInstance();
