const logger = require('@configs/logger');
const Http = require('@configs/http');
const { CXS_CALLBACK_API_KEY } = require('@configs/env');

class ChannelCallbackHttpClient extends Http {
  constructor() {
    super({});
    this.client.interceptors.request.use(
      this.#setAuthorizationHeader.bind(this),
      { synchronous: true }
    );
  }

  static getInstance() {
    if (!ChannelCallbackHttpClient.instance) {
      ChannelCallbackHttpClient.instance = new ChannelCallbackHttpClient();
    }
    return ChannelCallbackHttpClient.instance;
  }

  async #setAuthorizationHeader(config) {
    try {
      config.headers['x-api-key'] = CXS_CALLBACK_API_KEY;
      return config;
    } catch (err) {
      logger.error(
        err.config || err,
        'INTERCEPTOR[REQUEST]: HEADER_GENERATION_ERROR'
      );
      return Promise.reject('Unable to add callback header.');
    }
  }
}

module.exports = ChannelCallbackHttpClient.getInstance().client;
