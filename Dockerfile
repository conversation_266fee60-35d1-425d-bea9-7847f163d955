ARG NODE_VERSION=22.14.0
ARG ARTIFACTORY_URL

FROM $ARTIFACTORY_URL/hmd-docker-virtual/node:$NODE_VERSION-alpine AS base

# Stage 1: Dependencies
FROM base AS deps
ARG JFROG_PASSWORD
ARG JFROG_USERNAME

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

WORKDIR /home/<USER>/app

# Copy only the necessary files
COPY package*.json ./
RUN npm ci \
    --dist-url=https://$JFROG_USERNAME:$JFROG_PASSWORD@$ARTIFACTORY_URL/artifactory/hmd-npm-virtual \
    --verbose \
    --ignore-scripts \
    --omit=dev

# Stage 2: Runtime
FROM base as runner

RUN cp /dev/null /etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19/main" >>/etc/apk/repositories
RUN echo "https://$JFROG_USERNAME:$<EMAIL>/artifactory/hmd-alpinelinux/v3.19:/community" >>/etc/apk/repositories

# Install runtime dependencies
RUN apk --no-cache add \
    lz4-dev \
    cyrus-sasl \
    ca-certificates

WORKDIR /home/<USER>/app

COPY --from=deps /home/<USER>/app/node_modules ./node_modules
COPY . .

# Uncomment this if local testing/development
# RUN cp .env.example .env
USER node
ENTRYPOINT ["node", "index.js"]
